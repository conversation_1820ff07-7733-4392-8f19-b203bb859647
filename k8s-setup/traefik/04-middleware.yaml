apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: auth
  namespace: traefik-system
spec:
  basicAuth:
    secret: traefik-auth
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: secure-headers
  namespace: traefik-system
spec:
  headers:
    accessControlAllowMethods:
      - GET
      - OPTIONS
      - PUT
    accessControlAllowOriginList:
      - "*"
    accessControlMaxAge: 100
    hostsProxyHeaders:
      - "X-Forwarded-Host"
    referrerPolicy: "same-origin"
    customRequestHeaders:
      X-Forwarded-Proto: "https"
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: portainer-strip-prefix
  namespace: traefik-system
spec:
  stripPrefix:
    prefixes:
      - /po
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: traefik-dashboard
  namespace: traefik-system
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`traefik.localhost`) # Change this to your domain
      kind: Rule
      services:
        - name: api@internal
          kind: TraefikService
      middlewares:
        - name: auth
        - name: secure-headers
  tls:
    secretName: traefik-tls
---
apiVersion: v1
kind: Secret
metadata:
  name: traefik-auth
  namespace: traefik-system
type: Opaque
data:
  # Default: admin/admin (change this!)
  # Generate with: htpasswd -nb admin admin | base64
  users: YWRtaW46JGFwcjEkSDZ1c2o2N08kWTFsRDJzVjNHSFl6NTE5ZDNVVFg5LgoK

