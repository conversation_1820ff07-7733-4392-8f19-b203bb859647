apiVersion: v1
kind: ConfigMap
metadata:
  name: traefik-config
  namespace: traefik-system
data:
  traefik.yml: |
    # Static configuration
    global:
      sendAnonymousUsage: false
    
    # Entry points
    entryPoints:
      web:
        address: ":80"
        http:
          redirections:
            entryPoint:
              to: websecure
              scheme: https
      websecure:
        address: ":443"
    
    # Providers
    providers:
      kubernetescrd: {}
      kubernetesingress: {}
    
    # Certificate resolvers
    certificatesResolvers:
      letsencrypt:
        acme:
          email: <EMAIL>  # CHANGE THIS TO YOUR EMAIL
          storage: /data/acme.json
          httpChallenge:
            entryPoint: web
    
    # API and Dashboard
    api:
      dashboard: true
      insecure: false
    
    # Metrics
    metrics:
      prometheus:
        addEntryPointsLabels: true
        addServicesLabels: true
    
    # Access logs
    accessLog:
      format: json
    
    # Logs
    log:
      level: INFO
      format: json
