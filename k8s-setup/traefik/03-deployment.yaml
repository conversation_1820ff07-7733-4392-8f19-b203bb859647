apiVersion: apps/v1
kind: Deployment
metadata:
  name: traefik
  namespace: traefik-system
  labels:
    app.kubernetes.io/name: traefik
    app.kubernetes.io/instance: traefik
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: traefik
      app.kubernetes.io/instance: traefik
  template:
    metadata:
      labels:
        app.kubernetes.io/name: traefik
        app.kubernetes.io/instance: traefik
    spec:
      serviceAccountName: traefik-ingress-controller
      containers:
      - name: traefik
        image: traefik:v3.0
        args:
        - --api.dashboard=true
        - --entrypoints.web.address=:80
        - --entrypoints.websecure.address=:443
        - --providers.kubernetescrd=true
        - --providers.kubernetescrd.allowCrossNamespace=true
        - --providers.kubernetesingress=true
        - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
        - --certificatesresolvers.letsencrypt.acme.storage=/data/acme.json
        - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
        - --entrypoints.web.http.redirections.entrypoint.to=websecure
        - --entrypoints.web.http.redirections.entrypoint.scheme=https
        - --log.level=INFO
        - --accesslog=true
        - --metrics.prometheus=true
        - --ping=true
        ports:
        - name: web
          containerPort: 80
        - name: websecure
          containerPort: 443
        - name: admin
          containerPort: 8080
        resources:
          requests:
            memory: "100Mi"
            cpu: "100m"
          limits:
            memory: "300Mi"
            cpu: "300m"
        volumeMounts:
        - name: data
          mountPath: /data
        readinessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          runAsGroup: 65534
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: traefik-data
      securityContext:
        fsGroup: 65534
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: traefik-data
  namespace: traefik-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: traefik
  namespace: traefik-system
  labels:
    app.kubernetes.io/name: traefik
    app.kubernetes.io/instance: traefik
spec:
  type: LoadBalancer
  ports:
  - port: 80
    name: web
    targetPort: web
  - port: 443
    name: websecure
    targetPort: websecure
  - port: 8080
    name: admin
    targetPort: admin
  selector:
    app.kubernetes.io/name: traefik
    app.kubernetes.io/instance: traefik
