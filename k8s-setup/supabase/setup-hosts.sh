#!/bin/bash

# Script để cấu hình hosts file cho Supabase local domains

HOSTS_FILE="/etc/hosts"
DOMAINS=("supabase.local" "api.supabase.local")

echo "🔧 <PERSON><PERSON><PERSON> hình hosts file cho Supabase domains..."

# Kiểm tra quyền sudo
if [[ $EUID -eq 0 ]]; then
   echo "❌ Không chạy script này với quyền root. Chạy với user thường, script sẽ tự xin quyền sudo khi cần."
   exit 1
fi

# Backup hosts file
echo "💾 Backup hosts file..."
sudo cp $HOSTS_FILE $HOSTS_FILE.backup.$(date +%Y%m%d_%H%M%S)

# Thêm domains vào hosts file
echo "📝 Thêm domains vào hosts file..."
for domain in "${DOMAINS[@]}"; do
    if grep -q "$domain" $HOSTS_FILE; then
        echo "ℹ️  Domain $domain đã tồn tại trong hosts file"
    else
        echo "➕ Thêm $domain vào hosts file"
        echo "127.0.0.1    $domain" | sudo tee -a $HOSTS_FILE > /dev/null
    fi
done

echo ""
echo "✅ Cấu hình hosts file hoàn tất!"
echo ""
echo "🌐 Bây giờ bạn có thể truy cập:"
echo "   - Supabase Studio: https://supabase.local"
echo "   - Supabase API: https://api.supabase.local"
echo ""
echo "📝 Lưu ý: Cần apply ingress trước khi sử dụng:"
echo "   kubectl apply -f 02-ingress.yaml"
echo ""
echo "🔄 Để xóa cấu hình, chạy: ./cleanup-hosts.sh"
