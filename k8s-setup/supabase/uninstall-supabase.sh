#!/bin/bash

# Script gỡ cài đặt Supabase khỏi Kubernetes

set -e

echo "🗑️  Bắt đầu gỡ cài đặt Supabase..."

# Gỡ Helm release
echo "📦 Gỡ Helm release..."
helm uninstall supabase -n supabase

# Xóa namespace (tùy chọn)
read -p "Bạn có muốn xóa namespace 'supabase' không? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗂️  Xóa namespace supabase..."
    kubectl delete namespace supabase
    echo "✅ Namespace đã được xóa"
else
    echo "ℹ️  Giữ lại namespace supabase"
fi

echo "✅ Gỡ cài đặt Supabase hoàn tất!"
