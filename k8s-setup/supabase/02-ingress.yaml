apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: supabase-ingress
  namespace: supabase
  annotations:
    kubernetes.io/ingress.class: "traefik"
    traefik.ingress.kubernetes.io/router.entrypoints: web
    traefik.ingress.kubernetes.io/router.middlewares: default-redirect-https@kubernetescrd
spec:
  rules:
  - host: supabase.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: supabase-studio
            port:
              number: 3000
  - host: api.supabase.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: supabase-kong
            port:
              number: 8000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: supabase-ingress-tls
  namespace: supabase
  annotations:
    kubernetes.io/ingress.class: "traefik"
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
spec:
  tls:
  - hosts:
    - supabase.local
    - api.supabase.local
    secretName: traefik-tls-cert
  rules:
  - host: supabase.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: supabase-studio
            port:
              number: 3000
  - host: api.supabase.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: supabase-kong
            port:
              number: 8000
