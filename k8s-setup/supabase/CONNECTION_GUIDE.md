# 🔗 Hướng dẫn kết nối Supabase

## 📋 Thông tin kết nối cơ bản

### 🌐 URL truy cập

| Service | URL | Port | Trạng thái | Mô tả |
|---------|-----|------|-----------|-------|
| **Supabase Studio** | http://************:30001 | 30001 | ✅ Hoạt động | Dashboard quản lý (NodePort) |
| **PostgreSQL Database** | ************:5432 | 5432 | ✅ Hoạt động | Database trực tiếp |
| **Kong API Gateway** | http://************:8000 | 8000 | ✅ Hoạt động | REST API endpoint |

✅ **LƯU Ý**: Tất cả các services chính đang hoạt động bình thường. Có thể truy cập từ mạng LAN qua IP ************.

### 🔑 Thông tin xác thực

#### Dashboard Login
- **Username**: `supabase`
- **Password**: `this_password_is_insecure_and_should_be_updated`

#### Database Connection
- **Host**: `************` (truy cập từ mạng LAN) hoặc `supabase-db.supabase.svc.cluster.local` (trong cluster)
- **Port**: `5432`
- **Database**: `postgres`
- **Username**: `postgres`
- **Password**: `your-super-secret-and-long-postgres-password`

### 🔐 API Keys

⚠️ **QUAN TRỌNG**: Đây là các key development mặc định. Thay đổi cho production!

#### Anonymous Key (Public)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE
```

#### Service Role Key (Private)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.DaYlNEoUrrEn2Ig7tqibS-PHK5vgusbcbo7X36XVt4Q
```

#### JWT Secret
```
your-super-secret-jwt-token-with-at-least-32-characters-long
```

## 🔌 Connection Strings

### PostgreSQL Connection String
```
************************************************************************************/postgres
```

### Supabase Client URL
```
http://************:8000
```

## 📱 Cách sử dụng trong ứng dụng

### JavaScript/TypeScript
```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'http://************:8000'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### Python
```python
import os
from supabase import create_client, Client

url: str = "http://************:8000"
key: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
supabase: Client = create_client(url, key)
```

### Flutter/Dart
```dart
import 'package:supabase_flutter/supabase_flutter.dart';

await Supabase.initialize(
  url: 'http://************:8000',
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
);

final supabase = Supabase.instance.client;
```

## 🚀 Cách truy cập

### 1. Truy cập Supabase Studio
```bash
# Mở port-forward (nếu chưa mở)
kubectl port-forward svc/supabase-studio 3001:3001 -n supabase

# Hoặc sử dụng script có sẵn
cd k8s-setup/supabase
./port-forward.sh
```

Sau đó truy cập: **http://************:3001**

### 2. Truy cập API trực tiếp
API Gateway Kong đã được expose qua LoadBalancer:
```bash
curl http://************:8000/rest/v1/
```

### 3. Kết nối Database trực tiếp
```bash
# Port-forward PostgreSQL
kubectl port-forward svc/supabase-db 5432:5432 -n supabase

# Kết nối bằng psql
psql ************************************************************************************/postgres
```

## 🔧 Environment Variables

Tạo file `.env` cho ứng dụng của bạn:

```env
# Supabase Configuration
SUPABASE_URL=http://************:8000
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.DaYlNEoUrrEn2Ig7tqibS-PHK5vgusbcbo7X36XVt4Q

# Database Configuration
DATABASE_URL=************************************************************************************/postgres
DB_HOST=************
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your-super-secret-and-long-postgres-password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
```

## 🔒 Bảo mật

⚠️ **LƯU Ý QUAN TRỌNG**: Đây là cấu hình development với các key mặc định!

### Để sử dụng trong production:
1. **Thay đổi tất cả passwords và secrets**
2. **Tạo JWT keys mới**
3. **Cấu hình HTTPS**
4. **Thiết lập firewall và network policies**
5. **Backup database định kỳ**

### Thay đổi passwords:
```bash
# Tạo secret mới
kubectl create secret generic supabase-secrets-new \
  --from-literal=POSTGRES_PASSWORD="your-new-secure-password" \
  --from-literal=JWT_SECRET="your-new-jwt-secret-32-chars-min" \
  --from-literal=DASHBOARD_PASSWORD="your-new-dashboard-password" \
  -n supabase

# Cập nhật deployment để sử dụng secret mới
kubectl patch deployment supabase-db -n supabase -p '{"spec":{"template":{"spec":{"containers":[{"name":"postgres","env":[{"name":"POSTGRES_PASSWORD","valueFrom":{"secretKeyRef":{"name":"supabase-secrets-new","key":"POSTGRES_PASSWORD"}}}]}]}}}}'
```

## 🧪 Test kết nối

### Test API
```bash
# Test health check
curl http://************:8000/rest/v1/

# Test với authentication
curl -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE" \
     -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE" \
     http://************:8000/rest/v1/
```

### Test Database
```bash
# Test connection
pg_isready -h ************ -p 5432 -U postgres

# Test query
psql ************************************************************************************/postgres -c "SELECT version();"
```

## 🔧 Lịch sử sửa lỗi

### Supabase Studio CrashLoopBackOff (Đã sửa)
**Vấn đề:** Pod Studio bị crash liên tục do liveness probe sai cấu hình
**Nguyên nhân:** Liveness probe cố gắng kết nối đến hostname `studio` không tồn tại
**Giải pháp:**
- Đã patch deployment với liveness probe sử dụng `localhost:3000`
- Tạo NodePort service để expose port 30001 ra ngoài
- Studio hiện chạy ổn định trên http://************:30001

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs: `kubectl logs -f deployment/supabase-studio -n supabase`
2. Kiểm tra services: `kubectl get svc -n supabase`
3. Kiểm tra pods: `kubectl get pods -n supabase`
4. Restart services: `kubectl rollout restart deployment/supabase-studio -n supabase`
