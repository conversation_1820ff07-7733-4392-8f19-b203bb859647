apiVersion: v1
kind: ConfigMap
metadata:
  name: kong-config-simple
  namespace: supabase
  labels:
    app.kubernetes.io/component: gateway
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/name: kong
    app.kubernetes.io/part-of: supabase
data:
  kong.yml: |
    _format_version: "1.1"

    consumers:
      - username: DASHBOARD
      - username: anon
        keyauth_credentials:
          - key: ${ANON_KEY}
      - username: service_role
        keyauth_credentials:
          - key: ${SERVICE_ROLE_KEY}

    acls:
      - consumer: anon
        group: anon
      - consumer: service_role
        group: admin

    services:
      - name: auth-v1
        _comment: "GoTrue: /auth/v1/* -> http://supabase-auth:9999/*"
        url: http://supabase-auth:9999/
        routes:
          - name: auth-v1-all
            strip_path: true
            paths:
              - /auth/v1/
        plugins:
          - name: cors
          - name: key-auth
            config:
              hide_credentials: false
          - name: acl
            config:
              hide_groups_header: true
              allow:
                - admin
                - anon
      - name: rest-v1
        _comment: "PostgREST: /rest/v1/* -> http://supabase-rest:3000/*"
        url: http://supabase-rest:3000/
        routes:
          - name: rest-v1-all
            strip_path: true
            paths:
              - /rest/v1/
        plugins:
          - name: cors
          - name: key-auth
            config:
              hide_credentials: true
          - name: acl
            config:
              hide_groups_header: true
              allow:
                - admin
                - anon

    basicauth_credentials:
      - consumer: DASHBOARD
        username: ${DASHBOARD_USERNAME}
        password: ${DASHBOARD_PASSWORD}
