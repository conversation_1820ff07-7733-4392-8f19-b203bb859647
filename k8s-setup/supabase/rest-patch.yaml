apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-rest
  namespace: supabase
spec:
  template:
    spec:
      containers:
      - name: postgrest
        env:
        - name: PGRST_DB_URI
          value: "**************************************************************************************/postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: POSTGRES_PASSWORD
              name: supabase-secrets
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST
              name: supabase-config
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: supabase-config
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: supabase-config
        - name: PGRST_DB_SCHEMAS
          valueFrom:
            configMapKeyRef:
              key: PGRST_DB_SCHEMAS
              name: supabase-config
        - name: PGRST_DB_ANON_ROLE
          value: anon
        - name: PGRST_JWT_SECRET
          valueFrom:
            secretKeyRef:
              key: JWT_SECRET
              name: supabase-secrets
        - name: PGRST_DB_USE_LEGACY_GUCS
          value: "false"
        - name: PGRST_APP_SETTINGS_JWT_SECRET
          valueFrom:
            secretKeyRef:
              key: JWT_SECRET
              name: supabase-secrets
        - name: PGRST_APP_SETTINGS_JWT_EXP
          valueFrom:
            configMapKeyRef:
              key: JWT_EXPIRY
              name: supabase-config
