apiVersion: v1
kind: Service
metadata:
  name: supabase-studio-nodeport
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-studio
    app.kubernetes.io/instance: supabase
spec:
  type: NodePort
  ports:
  - port: 3001
    targetPort: 3000
    nodePort: 30001
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: supabase-studio
    app.kubernetes.io/instance: supabase
