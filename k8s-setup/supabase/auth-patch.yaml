apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-auth
  namespace: supabase
spec:
  template:
    spec:
      containers:
      - name: gotrue
        env:
        - name: GOTRUE_API_HOST
          value: "0.0.0.0"
        - name: GOTRUE_API_PORT
          value: "9999"
        - name: API_EXTERNAL_URL
          valueFrom:
            configMapKeyRef:
              key: API_EXTERNAL_URL
              name: supabase-config
        - name: GOTRUE_DB_DRIVER
          value: postgres
        - name: GOTRUE_DB_DATABASE_URL
          value: "********************************************************************************************/postgres"
