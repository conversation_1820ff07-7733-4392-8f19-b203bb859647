#!/bin/bash

# Script để xóa cấu hình hosts file cho Supabase local domains

HOSTS_FILE="/etc/hosts"
DOMAINS=("supabase.local" "api.supabase.local")

echo "🔧 Xóa cấu hình hosts file cho Supabase domains..."

# Kiểm tra quyền sudo
if [[ $EUID -eq 0 ]]; then
   echo "❌ Không chạy script này với quyền root. Chạy với user thường, script sẽ tự xin quyền sudo khi cần."
   exit 1
fi

# Backup hosts file
echo "💾 Backup hosts file..."
sudo cp $HOSTS_FILE $HOSTS_FILE.backup.$(date +%Y%m%d_%H%M%S)

# Xóa domains khỏi hosts file
echo "🗑️  Xóa domains khỏi hosts file..."
for domain in "${DOMAINS[@]}"; do
    if grep -q "$domain" $HOSTS_FILE; then
        echo "➖ Xóa $domain khỏi hosts file"
        sudo sed -i.bak "/127.0.0.1[[:space:]]*$domain/d" $HOSTS_FILE
    else
        echo "ℹ️  Domain $domain không tồn tại trong hosts file"
    fi
done

echo ""
echo "✅ Xóa cấu hình hosts file hoàn tất!"
