#!/bin/bash

# Script cài đặt Supabase trên Kubernetes với Docker Desktop
# Yêu cầu: Docker Desktop (đã bật Kubernetes), kube<PERSON><PERSON>, helm

set -e

echo "🚀 Bắt đầu cài đặt Supabase trên Kubernetes..."

# Kiểm tra kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl không được tìm thấy. Vui lòng cài đặt kubectl."
    exit 1
fi

# Kiểm tra helm
if ! command -v helm &> /dev/null; then
    echo "❌ Helm không được tìm thấy. Đang cài đặt Helm..."
    
    # Cài Helm dựa trên hệ điều hành
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install helm
        else
            echo "❌ Homebrew không được tìm thấy. Vui lòng cài đặt Homebrew hoặc cài Helm thủ công."
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    else
        echo "❌ Hệ điều hành không được hỗ trợ. Vui lòng cài Helm thủ công."
        exit 1
    fi
fi

echo "✅ Helm đã sẵn sàng"

# Tạo namespace
echo "📁 Tạo namespace supabase..."
kubectl apply -f 01-namespace.yaml

# Add Helm repo
echo "📦 Thêm Supabase Helm repository..."
helm repo add supabase-community https://supabase-community.github.io/helm-charts
helm repo update

# Cài đặt Supabase
echo "🔧 Cài đặt Supabase..."
if [ -f "values.yaml" ]; then
    echo "📝 Sử dụng file cấu hình values.yaml..."
    helm install supabase supabase-community/supabase -n supabase -f values.yaml
else
    echo "📝 Sử dụng cấu hình mặc định..."
    helm install supabase supabase-community/supabase -n supabase
fi

# Kiểm tra trạng thái
echo "🔍 Kiểm tra trạng thái các service..."
kubectl get all -n supabase

echo ""
echo "✅ Cài đặt Supabase hoàn tất!"
echo ""
echo "🌐 Để truy cập Supabase Studio, chạy lệnh sau:"
echo "kubectl port-forward svc/supabase-studio 3000:3000 -n supabase"
echo ""
echo "Sau đó truy cập: http://localhost:3000"
echo ""
echo "📚 Để xem logs: kubectl logs -f deployment/supabase-studio -n supabase"
echo "🗑️  Để gỡ cài đặt: helm uninstall supabase -n supabase"
