# 🚀 Supabase Quick Start

## ✅ Trạng thái hiện tại

Supabase đã được cài đặt thành công với các services chính đang hoạt động:

- ✅ **Supabase Studio** - Dashboard quản lý
- ✅ **PostgreSQL Database** - Database chính
- ✅ **Kong API Gateway** - REST API endpoint
- ✅ **Storage Service** - File storage
- ✅ **Realtime Service** - Real-time subscriptions
- ✅ **REST API** - PostgREST API

## 🔗 Kết nối nhanh

### 1. Truy cập Dashboard
```bash
# Mở port-forward cho Studio (nếu chưa mở)
kubectl port-forward svc/supabase-studio 3000:3000 -n supabase

# Truy cập: http://localhost:3000
# Username: supabase
# Password: this_password_is_insecure_and_should_be_updated
```

### 2. Kết nối API
```bash
# API URL: http://localhost:8000
# Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE
```

### 3. Kết nối Database
```bash
# Mở port-forward cho PostgreSQL
kubectl port-forward svc/supabase-db 5432:5432 -n supabase

# Connection String:
# postgresql://postgres:your-super-secret-and-long-postgres-password@localhost:5432/postgres
```

## 🛠️ Scripts tiện ích

| Script | Mô tả |
|--------|-------|
| `./port-forward.sh` | Mở port-forward cho Studio |
| `./open-all-ports.sh` | Mở tất cả port-forward |
| `./check-connection.sh` | Kiểm tra trạng thái kết nối |
| `./fix-supabase.sh` | Khắc phục vấn đề và restart services |

## 📱 Sử dụng trong code

### JavaScript/TypeScript
```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'http://localhost:8000',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE'
)
```

### Environment Variables
```env
SUPABASE_URL=http://localhost:8000
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE
DATABASE_URL=postgresql://postgres:your-super-secret-and-long-postgres-password@localhost:5432/postgres
```

## ⚠️ Lưu ý bảo mật

- Đây là cấu hình development với keys mặc định
- Thay đổi tất cả passwords và secrets cho production
- Xem file `CONNECTION_GUIDE.md` để biết chi tiết về bảo mật

## 📚 Tài liệu chi tiết

- `CONNECTION_GUIDE.md` - Hướng dẫn kết nối chi tiết
- `README.md` - Hướng dẫn cài đặt đầy đủ
- `.env.example` - Template environment variables

## 🆘 Troubleshooting

Nếu gặp vấn đề:
1. Chạy `./check-connection.sh` để kiểm tra trạng thái
2. Chạy `./fix-supabase.sh` để khắc phục
3. Xem logs: `kubectl logs -f deployment/supabase-studio -n supabase`
4. Restart: `kubectl rollout restart deployment/supabase-studio -n supabase`
