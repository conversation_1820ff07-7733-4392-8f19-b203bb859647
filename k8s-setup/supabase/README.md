# Hướng dẫn cài đặt Supabase trên Kubernetes

Hướng dẫn này giúp bạn cài đặt Supabase self-hosted trên Kubernetes với Docker Desktop một cách đơn giản nhất.

## Yêu cầu tối thiểu

- **Docker Desktop** (đã bật Kubernetes)
- **kubectl** (điều khiển cụm K8s)
- **Helm** (quản lý chart cài đặt cho K8s) - sẽ được cài tự động nếu chưa có
- Máy tính cá nhân đang chạy Windows/macOS/Linux

## Cấu trúc file

```
k8s-setup/supabase/
├── 01-namespace.yaml          # Tạo namespace cho Supabase
├── values.yaml                # Cấu hình Supabase
├── install-supabase.sh        # Script cài đặt tự động
├── port-forward.sh            # Script mở port-forward
├── uninstall-supabase.sh      # Script gỡ cài đặt
└── README.md                  # Hướng dẫn này
```

## C<PERSON>ch cài đặt

### 1. <PERSON>ài đặt tự động (Khuyến nghị)

```bash
cd k8s-setup/supabase
chmod +x install-supabase.sh
./install-supabase.sh
```

Script sẽ tự động:
- Kiểm tra và cài đặt Helm nếu cần
- Tạo namespace `supabase`
- Thêm Supabase Helm repository
- Cài đặt Supabase với cấu hình từ `values.yaml`

### 2. Cài đặt thủ công

#### Bước 1: Cài Helm (nếu chưa có)

**macOS:**
```bash
brew install helm
```

**Ubuntu/Linux:**
```bash
sudo snap install helm --classic
# hoặc
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

**Windows (Chocolatey):**
```bash
choco install kubernetes-helm
```

#### Bước 2: Tạo namespace
```bash
kubectl apply -f 01-namespace.yaml
```

#### Bước 3: Add Helm repo & cài Supabase
```bash
helm repo add supabase-community https://supabase-community.github.io/helm-charts
helm repo update
helm install supabase supabase-community/supabase -n supabase -f values.yaml
```

## Truy cập Supabase Studio

### Cách 1: Sử dụng script
```bash
chmod +x port-forward.sh
./port-forward.sh
```

### Cách 2: Chạy lệnh trực tiếp
```bash
kubectl port-forward svc/supabase-studio 3000:3000 -n supabase
```

Sau đó truy cập: **http://localhost:3000**

## Kiểm tra trạng thái

```bash
# Xem tất cả resources trong namespace supabase
kubectl get all -n supabase

# Xem logs của Studio
kubectl logs -f deployment/supabase-studio -n supabase

# Xem logs của PostgreSQL
kubectl logs -f deployment/supabase-postgresql -n supabase
```

## Cấu hình nâng cao

Chỉnh sửa file `values.yaml` để:
- Thay đổi mật khẩu PostgreSQL
- Cấu hình resources (CPU, Memory)
- Bật/tắt các service
- Cấu hình storage
- Thiết lập ingress

Sau khi chỉnh sửa, cập nhật cài đặt:
```bash
helm upgrade supabase supabase-community/supabase -n supabase -f values.yaml
```

## Gỡ cài đặt

### Cách 1: Sử dụng script
```bash
chmod +x uninstall-supabase.sh
./uninstall-supabase.sh
```

### Cách 2: Gỡ thủ công
```bash
# Gỡ Helm release
helm uninstall supabase -n supabase

# Xóa namespace (tùy chọn)
kubectl delete namespace supabase
```

## Troubleshooting

### Lỗi thường gặp

1. **Pod không khởi động được:**
   ```bash
   kubectl describe pod <pod-name> -n supabase
   ```

2. **Service không accessible:**
   ```bash
   kubectl get svc -n supabase
   kubectl describe svc supabase-studio -n supabase
   ```

3. **PostgreSQL connection issues:**
   ```bash
   kubectl logs deployment/supabase-postgresql -n supabase
   ```

### Reset hoàn toàn
```bash
helm uninstall supabase -n supabase
kubectl delete namespace supabase
kubectl create namespace supabase
# Cài lại từ đầu
```

## Lưu ý quan trọng

- ⚠️ **Đây là cách cài tối giản phù hợp phát triển, test**
- 🔒 **Khi triển khai thật, hãy đọc kỹ tài liệu về bảo mật và backup dữ liệu**
- 📚 **Chart Helm chính thức:** https://github.com/supabase-community/supabase-kubernetes
- 🔄 **Dữ liệu được lưu trong PersistentVolume, sẽ không mất khi restart pod**

## Tài liệu tham khảo

- [Supabase Official Documentation](https://supabase.com/docs)
- [Supabase Kubernetes Helm Charts](https://github.com/supabase-community/supabase-kubernetes)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Helm Documentation](https://helm.sh/docs/)
