#!/bin/bash

# Script khắc phục các vấn đề Supabase

set -e

echo "🔧 Khắc phục các vấn đề Supabase..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Kiểm tra kubectl
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl không được tìm thấy.${NC}"
    exit 1
fi

# Kiểm tra namespace
if ! kubectl get namespace supabase &> /dev/null; then
    echo -e "${RED}❌ Namespace supabase không tồn tại.${NC}"
    exit 1
fi

echo -e "${YELLOW}📊 Kiểm tra trạng thái hiện tại...${NC}"
kubectl get pods -n supabase

echo ""
echo -e "${YELLOW}🔄 Restart các deployments có vấn đề...${NC}"

# Restart các deployments
deployments=("supabase-studio" "supabase-kong" "supabase-auth" "supabase-rest" "supabase-storage" "supabase-realtime" "supabase-functions" "supabase-analytics")

for deployment in "${deployments[@]}"; do
    echo -e "${YELLOW}🔄 Restarting $deployment...${NC}"
    kubectl rollout restart deployment/$deployment -n supabase
done

echo ""
echo -e "${YELLOW}⏳ Chờ các pods khởi động lại...${NC}"
sleep 10

echo ""
echo -e "${YELLOW}📊 Kiểm tra trạng thái sau khi restart...${NC}"
kubectl get pods -n supabase

echo ""
echo -e "${YELLOW}🔍 Kiểm tra logs của các pods có vấn đề...${NC}"

# Kiểm tra logs của Studio
echo -e "${YELLOW}📋 Logs của Supabase Studio:${NC}"
kubectl logs -n supabase deployment/supabase-studio --tail=5 || echo -e "${RED}❌ Không thể lấy logs của Studio${NC}"

echo ""
echo -e "${YELLOW}📋 Logs của Kong:${NC}"
kubectl logs -n supabase deployment/supabase-kong --tail=5 || echo -e "${RED}❌ Không thể lấy logs của Kong${NC}"

echo ""
echo -e "${GREEN}✅ Hoàn thành restart. Chờ 1-2 phút để các services khởi động hoàn toàn.${NC}"
echo ""
echo -e "${YELLOW}💡 Gợi ý:${NC}"
echo "1. Chạy ./check-connection.sh để kiểm tra trạng thái"
echo "2. Chạy ./port-forward.sh để mở port-forward"
echo "3. Nếu vẫn có vấn đề, xem logs chi tiết: kubectl logs -f deployment/[service-name] -n supabase"
