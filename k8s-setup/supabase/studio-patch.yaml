apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-studio
  namespace: supabase
spec:
  template:
    spec:
      containers:
      - name: studio
        livenessProbe:
          exec:
            command:
            - node
            - -e
            - "fetch('http://localhost:3000/api/platform/profile').then((r) => {if (r.status !== 200) throw new Error(r.status)})"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/platform/profile
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1"
