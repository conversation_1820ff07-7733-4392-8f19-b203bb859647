#!/bin/bash

# Script kiểm tra kết nối Supabase

set -e

echo "🔍 Kiểm tra kết nối Supabase..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if port is open
check_port() {
    local host=$1
    local port=$2
    local service=$3
    
    if nc -z $host $port 2>/dev/null; then
        echo -e "${GREEN}✅ $service ($host:$port) - CONNECTED${NC}"
        return 0
    else
        echo -e "${RED}❌ $service ($host:$port) - NOT ACCESSIBLE${NC}"
        return 1
    fi
}

# Function to check HTTP endpoint
check_http() {
    local url=$1
    local service=$2
    
    if curl -s --max-time 5 $url > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service ($url) - RESPONDING${NC}"
        return 0
    else
        echo -e "${RED}❌ $service ($url) - NOT RESPONDING${NC}"
        return 1
    fi
}

echo ""
echo "📊 Kiểm tra Kubernetes Services..."

# Check if namespace exists
if ! kubectl get namespace supabase &> /dev/null; then
    echo -e "${RED}❌ Namespace supabase không tồn tại${NC}"
    exit 1
fi

# Check pods status
echo ""
echo "🔍 Trạng thái Pods:"
kubectl get pods -n supabase --no-headers | while read line; do
    name=$(echo $line | awk '{print $1}')
    status=$(echo $line | awk '{print $3}')
    
    if [[ "$status" == "Running" ]]; then
        echo -e "${GREEN}✅ $name - $status${NC}"
    else
        echo -e "${RED}❌ $name - $status${NC}"
    fi
done

# Check services
echo ""
echo "🔍 Trạng thái Services:"
kubectl get svc -n supabase --no-headers | while read line; do
    name=$(echo $line | awk '{print $1}')
    type=$(echo $line | awk '{print $2}')
    ports=$(echo $line | awk '{print $5}')
    
    echo -e "${YELLOW}📋 $name ($type) - Ports: $ports${NC}"
done

echo ""
echo "🌐 Kiểm tra kết nối Port-Forward..."

# Check common ports
check_port "localhost" "3000" "Supabase Studio"
check_port "localhost" "8000" "Kong API Gateway"
check_port "localhost" "5432" "PostgreSQL"
check_port "localhost" "5000" "Storage"
check_port "localhost" "4000" "Realtime"

echo ""
echo "🔗 Kiểm tra HTTP Endpoints..."

# Check HTTP endpoints
check_http "http://localhost:3000" "Supabase Studio"
check_http "http://localhost:8000/rest/v1/" "REST API"
check_http "http://localhost:5000/status" "Storage API"

echo ""
echo "🔑 Kiểm tra API với Authentication..."

# Test API with auth
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"

if curl -s --max-time 5 \
   -H "apikey: $ANON_KEY" \
   -H "Authorization: Bearer $ANON_KEY" \
   http://localhost:8000/rest/v1/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ API Authentication - WORKING${NC}"
else
    echo -e "${RED}❌ API Authentication - FAILED${NC}"
fi

echo ""
echo "🗄️  Kiểm tra Database Connection..."

# Check database connection
if command -v psql &> /dev/null; then
    if psql postgresql://postgres:your-super-secret-and-long-postgres-password@localhost:5432/postgres -c "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PostgreSQL Connection - WORKING${NC}"
    else
        echo -e "${RED}❌ PostgreSQL Connection - FAILED${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  psql không được cài đặt, bỏ qua kiểm tra database${NC}"
fi

echo ""
echo "📋 Tóm tắt thông tin kết nối:"
echo "- Supabase Studio: http://localhost:3000"
echo "- API Gateway: http://localhost:8000"
echo "- PostgreSQL: localhost:5432"
echo "- Storage: http://localhost:5000"
echo "- Realtime: http://localhost:4000"
echo ""
echo "📖 Xem chi tiết trong file CONNECTION_GUIDE.md"
