#!/bin/bash

# Kubernetes Setup Script for Docker Desktop
# This script installs Traefik with Let's Encrypt and Port<PERSON>r

set -e

echo "🚀 Setting up Kubernetes cluster with <PERSON>raefik and Portainer..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}kubectl is not installed or not in PATH${NC}"
    exit 1
fi

# Check if cluster is running
if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}Kubernetes cluster is not running. Please start Docker Desktop with Kubernetes enabled.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Kubernetes cluster is running${NC}"

# Create namespaces
echo -e "${YELLOW}📁 Creating namespaces...${NC}"
kubectl create namespace traefik-system --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace portainer --dry-run=client -o yaml | kubectl apply -f -

# Install Traefik CRDs
echo -e "${YELLOW}📋 Installing Traefik CRDs...${NC}"
kubectl apply -f https://raw.githubusercontent.com/traefik/traefik/v3.0/docs/content/reference/dynamic-configuration/kubernetes-crd-definition-v1.yml

# Install Traefik
echo -e "${YELLOW}🌐 Installing Traefik...${NC}"
kubectl apply -f traefik/01-rbac.yaml
kubectl apply -f traefik/03-deployment.yaml
kubectl apply -f traefik/04-middleware.yaml

# Wait for Traefik to be ready
echo -e "${YELLOW}⏳ Waiting for Traefik to be ready...${NC}"
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=traefik -n traefik-system --timeout=300s

# Install Portainer
echo -e "${YELLOW}🐳 Installing Portainer...${NC}"
kubectl apply -f portainer/

# Wait for Portainer to be ready
echo -e "${YELLOW}⏳ Waiting for Portainer to be ready...${NC}"
kubectl wait --for=condition=ready pod -l app=portainer -n portainer --timeout=300s

echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Access Information:${NC}"
echo "• Traefik Dashboard: http://traefik.localhost (if you have local DNS setup)"
echo "• Portainer: http://portainer.localhost (if you have local DNS setup)"
echo ""
echo -e "${YELLOW}🔧 To access services via localhost, you can use port forwarding:${NC}"
echo "• Traefik Dashboard: kubectl port-forward -n traefik-system svc/traefik 8080:8080"
echo "• Portainer: kubectl port-forward -n portainer svc/portainer 9000:9000"
echo ""
echo -e "${YELLOW}📝 Note: For production use, update the email in traefik/04-middleware.yaml${NC}"
echo "and configure proper DNS records for your domain."
