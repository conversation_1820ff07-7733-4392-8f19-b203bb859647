apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: portainer
  namespace: portainer
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`drive-migration.osp.com.vn`) && PathPrefix(`/po`)
      kind: Rule
      services:
        - name: portainer
          port: 9000
      middlewares:
        - name: portainer-strip-prefix
          namespace: traefik-system
        - name: secure-headers
          namespace: traefik-system
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: portainer-http
  namespace: portainer
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`drive-migration.osp.com.vn`) && PathPrefix(`/po`)
      kind: Rule
      services:
        - name: portainer
          port: 9000
      middlewares:
        - name: https-redirect
          namespace: default

