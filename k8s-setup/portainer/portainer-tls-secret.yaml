apiVersion: v1
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURIVENDQWdXZ0F3SUJBZ0lVSEM1U3lxUG56elFZNHJoY2N0RlJUZDZJMko0d0RRWUpLb1pJaHZjTkFRRUwKQlFBd0hqRWNNQm9HQTFVRUF3d1RjRzl5ZEdGcGJtVnlMbXh2WTJGc2FHOXpkREFlRncweU5UQTNNRGN3TWpRNQpOVEJhRncweU5qQTNNRGN3TWpRNU5UQmFNQjR4SERBYUJnTlZCQU1NRTNCdmNuUmhhVzVsY2k1c2IyTmhiR2h2CmMzUXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFDNThld1BTWHh6bngzVSszZlgKVVpOdjB6Y1U0YmJCR0liSGk0RW1vVG5FWmFJUnJaVXdhYXlocForeDF6Z0swV25SUExNUVdwemFQUytncUJIYQo3RkVxRno3dnY0UFFxNjQvMm5UUEpnVGY0a3FlR2VEcFpwczlhbUpIcCtJaVNOdHowNy9HR0NlOTlweWtsY2lvCnh2d2lYNHh3cnE1Tm9SUzFxTGNVdEhsY29XS0Z0RUNOUEdTay9CeFBFRW9GbjJTUWZENWNhM3l0aWhCZ1YxZ0IKTTR2NCt1N0hNYmhFUGFjaU5PRlNTVmxsK2NPYkZvSjRLakd3VFJDUWNKSjl5SldRMjVlck9zTDJqc2NTbWU3RApqaE9XSm1xTmg3WFhZcmJDZGFRQXpFQmsrenhVcE4xUzdXZGtXMm9rN0pDWEh3SWYyZVkvUUlKMjloNVdpZTN0CmtFZFJBZ01CQUFHalV6QlJNQjBHQTFVZERnUVdCQlJuRjFEdUd2TVZoQTRkYTRnTE8vTHRyQ2tZZmpBZkJnTlYKSFNNRUdEQVdnQlJuRjFEdUd2TVZoQTRkYTRnTE8vTHRyQ2tZZmpBUEJnTlZIUk1CQWY4RUJUQURBUUgvTUEwRwpDU3FHU0liM0RRRUJDd1VBQTRJQkFRQkxhOFJkRk9zQUhCM1AyYlNzVFNwbHVRd0tTdUdFR3g5VHVBQU1aU0diCktTckFiUENlQVBTOHRRZGUwQWJBRkROWVZSMXZob2pUVU9XeUFPanRKWTREejNFRXZSWjlkTkZtdmJEQk9WanQKUElCUnNjaWMwamt1NDFXM08zckM1SEpFVTNVY1hrcFByc3gzbUlENUVIVmlkc2cyakhJSWZWbTdjcmNRWWY3RgppeFllQ3NLMG1lODUrZFZFOHlodUtLR3Y0QzdzQ0JxMW1pUXFLRUtXV0wwT1lyc0ZvemYwdC9ic2R1bHlWcUZLCkFYcXd6cXBUejB6bzZFVTg5cVc4VGoyN0ladEFWQkJ4S3diTWY2RU4wNGhlYnExTWtweTN5YXhiek55SnZjY0UKUkQ5ZXN6VVRqWHR1ZGtOOUhxeEhXUEhQekl5NTRYZEIzNVRqU1FKUnNLN0QKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  creationTimestamp: null
  name: portainer-tls
  namespace: portainer
type: kubernetes.io/tls
