# Redis Service Deployment

## 🚀 Successfully Deployed!

### 📋 Service Details:
- **Docker Image**: `redis:7-alpine`
- **Container Port**: 6379
- **Service Type**: ClusterIP + NodePort
- **Password**: `redis123`
- **Namespace**: default
- **Storage**: 1GB persistent volume

### 🌐 Network Configuration:
- **Internal Access**: `redis:6379` (within cluster)
- **External Access**: `localhost:30379` (NodePort)
- **Persistent Storage**: `/data` volume mounted

### 🔧 Access Methods:

#### Internal Access (from within cluster):
```bash
redis-cli -h redis -p 6379 -a redis123
```

#### External Access (from localhost):
```bash
redis-cli -h localhost -p 30379 -a redis123
```

#### Port Forwarding for Development:
```bash
kubectl port-forward svc/redis 6379:6379
# Access: redis-cli -h localhost -p 6379 -a redis123
```

### 📁 File Structure:
```
redis/
└── 01-deployment.yaml     # Deployment, PVC, and Services
```

### 🛠️ Management Commands:

#### Deploy Redis:
```bash
./deploy-redis.sh
```

#### Remove Redis:
```bash
./cleanup-redis.sh
```

#### Check Status:
```bash
kubectl get pods -l app=redis
kubectl get services redis redis-nodeport
kubectl get pvc redis-pvc
```

#### View Logs:
```bash
kubectl logs -l app=redis
```

#### Connect to Redis CLI:
```bash
kubectl exec -it deployment/redis -- redis-cli -a redis123
```

### 🔒 Security Features:
- Password authentication enabled (`redis123`)
- Resource limits configured
- Health checks (liveness and readiness probes)
- Persistent data storage

### 📊 Monitoring:
- **Liveness Probe**: TCP socket check on port 6379
- **Readiness Probe**: Redis ping command
- **Resource Limits**: 256Mi memory, 200m CPU

### 🔄 Data Persistence:
- Uses PersistentVolumeClaim for data storage
- Data persists across pod restarts
- Append-only file (AOF) enabled for durability

## Configuration Options:

### Environment Variables:
The Redis deployment includes these configuration options:
- `--appendonly yes` - Enable AOF persistence
- `--requirepass redis123` - Set authentication password

### Storage:
- **Storage Class**: hostpath
- **Access Mode**: ReadWriteOnce
- **Size**: 1Gi

### Resources:
- **Requests**: 128Mi memory, 100m CPU
- **Limits**: 256Mi memory, 200m CPU
