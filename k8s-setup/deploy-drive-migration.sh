#!/bin/bash

# Deploy drive-migration service to Kubernetes

set -e

echo "🚀 Deploying drive-migration service..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}kubectl is not installed or not in PATH${NC}"
    exit 1
fi

# Check if cluster is running
if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}Kubernetes cluster is not running. Please start Docker Desktop with Kubernetes enabled.${NC}"
    exit 1
fi

# Check if Traefik is running
if ! kubectl get pods -n traefik-system -l app.kubernetes.io/name=traefik --no-headers 2>/dev/null | grep -q "Running"; then
    echo -e "${RED}Traefik is not running. Please run ./setup.sh first to install Traefik.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Deploy the service
echo -e "${YELLOW}📦 Deploying drive-migration service...${NC}"
kubectl apply -f drive-migration/

# Wait for deployment to be ready
echo -e "${YELLOW}⏳ Waiting for drive-migration to be ready...${NC}"
kubectl wait --for=condition=available deployment/drive-migration --timeout=300s

echo -e "${GREEN}🎉 Drive-migration service deployed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Service Information:${NC}"
echo "• Service Name: drive-migration"
echo "• Namespace: default"
echo "• Domain: https://drive-migration.osp.com.vn"
echo "• Container Port: 3000"
echo ""
echo -e "${YELLOW}🔧 Access Information:${NC}"
echo "• Production URL: https://drive-migration.osp.com.vn"
echo "• Local Access (port forwarding): kubectl port-forward svc/drive-migration 3000:3000"
echo ""
echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "1. Configure DNS to point drive-migration.osp.com.vn to your cluster's external IP"
echo "2. Let's Encrypt will automatically provision SSL certificates"
echo "3. The service will be accessible via HTTPS with automatic HTTP redirect"
echo ""
echo -e "${YELLOW}🔍 Status Check:${NC}"
kubectl get pods -l app=drive-migration
kubectl get svc drive-migration
kubectl get ingressroute drive-migration
