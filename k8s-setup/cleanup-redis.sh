#!/bin/bash

# Cleanup Redis deployment from Kubernetes

set -e

echo "🧹 Cleaning up Redis deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}kubectl is not installed or not in PATH${NC}"
    exit 1
fi

# Check if cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}Cannot connect to Kubernetes cluster${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Remove Redis deployment
echo -e "${YELLOW}🗑️ Removing Redis deployment...${NC}"
kubectl delete -f redis/01-deployment.yaml --ignore-not-found=true

echo -e "${GREEN}✅ Redis cleanup completed successfully!${NC}"
