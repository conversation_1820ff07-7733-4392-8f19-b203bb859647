# Kubernetes Setup with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>r

This repository contains scripts and configurations to set up a Kubernetes cluster with <PERSON><PERSON><PERSON><PERSON> (as ingress controller with Let's Encrypt) and <PERSON><PERSON><PERSON> (for cluster management) on Docker Desktop.

## Prerequisites

- Docker Desktop with Kubernetes enabled
- kubectl configured to access your cluster
- (Optional) htpasswd for generating basic auth credentials

## Quick Start

1. **<PERSON>lone and navigate to the setup directory:**
   ```bash
   cd k8s-setup
   ```

2. **Make scripts executable:**
   ```bash
   chmod +x setup.sh cleanup.sh
   ```

3. **Configure your email for Let's Encrypt:**
   Edit `traefik/02-configmap.yaml` and replace `<EMAIL>` with your actual email address.

4. **Run the setup script:**
   ```bash
   ./setup.sh
   ```

## Configuration Details

### Traefik Configuration

- **Ports**: 80 (HTTP, redirects to HTTPS), 443 (HTTPS), 8080 (Dashboard)
- **Let's Encrypt**: Automatic certificate provisioning and renewal
- **Dashboard**: Protected with basic auth (default: admin/admin)
- **Security**: HTTP to HTTPS redirect, secure headers middleware

### Portainer Configuration

- **Port**: 9000 (Web UI)
- **Storage**: 10GB persistent volume
- **Permissions**: Cluster admin access
- **Security**: Non-root user, read-only filesystem

## Accessing Services

### Local Development (localhost)

Use port forwarding to access services:

```bash
# Traefik Dashboard
kubectl port-forward -n traefik-system svc/traefik 8080:8080
# Access: http://localhost:8080

# Portainer
kubectl port-forward -n portainer svc/portainer 9000:9000
# Access: http://localhost:9000

# Drive Migration Service
kubectl port-forward svc/drive-migration 3000:3000
# Access: http://localhost:3000
```

### Production (with domain)

1. **Update domain names** in the following files:
   - `traefik/04-middleware.yaml` - Change `traefik.localhost` to your domain
   - `portainer/03-ingress.yaml` - Change `portainer.localhost` to your domain

2. **Configure DNS** to point your domains to your cluster's external IP

3. **Access services**:
   - Traefik Dashboard: `https://traefik.yourdomain.com`
   - Portainer: `https://portainer.yourdomain.com`
   - Drive Migration: `https://drive-migration.osp.com.vn`

## Security Considerations

### Change Default Credentials

The default Traefik dashboard credentials are `admin/admin`. To change them:

1. Generate new credentials:
   ```bash
   htpasswd -nb newuser newpassword | base64
   ```

2. Update the `users` field in `traefik/04-middleware.yaml` with the generated hash.

### Email Configuration

Update the email address in `traefik/02-configmap.yaml` for Let's Encrypt notifications.

## Monitoring and Troubleshooting

### Check Pod Status
```bash
# Check all pods
kubectl get pods -A

# Check Traefik pods
kubectl get pods -n traefik-system

# Check Portainer pods
kubectl get pods -n portainer
```

### View Logs
```bash
# Traefik logs
kubectl logs -n traefik-system deployment/traefik

# Portainer logs
kubectl logs -n portainer deployment/portainer
```

### Check Services
```bash
# List all services
kubectl get svc -A

# Check Traefik service
kubectl get svc -n traefik-system

# Check Portainer service
kubectl get svc -n portainer
```

## Customization

### Traefik Configuration

Edit `traefik/02-configmap.yaml` to customize:
- Certificate resolvers (DNS challenge for wildcard certificates)
- Logging levels
- Metrics collection
- Additional middleware

### Portainer Configuration

Edit `portainer/02-deployment.yaml` to customize:
- Resource limits
- Storage size
- Environment variables

## Cleanup

To remove all resources:

```bash
./cleanup.sh
```

## File Structure

```
k8s-setup/
├── setup.sh                    # Main setup script
├── cleanup.sh                  # Cleanup script
├── traefik/
│   ├── 01-rbac.yaml            # RBAC configuration
│   ├── 02-configmap.yaml       # Traefik configuration
│   ├── 03-deployment.yaml      # Traefik deployment and service
│   └── 04-middleware.yaml      # Middlewares and ingress routes
└── portainer/
    ├── 01-rbac.yaml            # RBAC configuration
    ├── 02-deployment.yaml      # Portainer deployment and service
    └── 03-ingress.yaml         # Ingress route configuration
```

## Advanced Configuration

### DNS Challenge for Wildcard Certificates

For wildcard certificates, uncomment and configure the DNS challenge in `traefik/02-configmap.yaml`:

```yaml
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /data/acme.json
      dnsChallenge:
        provider: cloudflare  # or other supported providers
        resolvers:
          - "*******:53"
          - "*******:53"
```

### Multiple Environments

Create separate configuration files for different environments (dev, staging, prod) by copying and modifying the YAML files.

## Support

For issues and questions:
- Check the Traefik documentation: https://doc.traefik.io/traefik/
- Check the Portainer documentation: https://docs.portainer.io/
- Verify your Kubernetes cluster is running properly with Docker Desktop
