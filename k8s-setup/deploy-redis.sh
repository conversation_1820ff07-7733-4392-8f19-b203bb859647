#!/bin/bash

# Deploy Redis service to Kubernetes

set -e

echo "🚀 Deploying Redis service..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}kubectl is not installed or not in PATH${NC}"
    exit 1
fi

# Check if cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}Cannot connect to Kubernetes cluster${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Deploy Redis
echo -e "${YELLOW}📦 Deploying Redis service...${NC}"
kubectl apply -f redis/01-deployment.yaml

# Wait for Redis to be ready
echo -e "${YELLOW}⏳ Waiting for Redis to be ready...${NC}"
kubectl wait --for=condition=ready pod -l app=redis --timeout=120s

# Check deployment status
echo -e "${GREEN}📊 Checking Redis deployment status...${NC}"
kubectl get pods -l app=redis
kubectl get services redis redis-nodeport

echo ""
echo -e "${GREEN}🎉 Redis deployment completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Redis Access Information:${NC}"
echo -e "  • Internal Access: redis:6379"
echo -e "  • External Access: localhost:30379 (NodePort)"
echo -e "  • Password: redis123"
echo ""
echo -e "${YELLOW}🔗 Connection Examples:${NC}"
echo -e "  # Connect from within cluster:"
echo -e "  redis-cli -h redis -p 6379 -a redis123"
echo ""
echo -e "  # Connect from localhost:"
echo -e "  redis-cli -h localhost -p 30379 -a redis123"
echo ""
echo -e "  # Port forward for local development:"
echo -e "  kubectl port-forward svc/redis 6379:6379"
echo ""
