# Drive Migration Service Deployment

## 🚀 Successfully Deployed!

### 📋 Service Details:
- **Docker Image**: `vietprogrammer/drive-migration:latest`
- **Container Port**: 3000
- **Service Type**: ClusterIP
- **Domain**: `https://drive-migration.osp.com.vn`
- **Namespace**: default

### 🌐 Network Configuration:
- **HTTP**: Automatically redirects to HTTPS
- **HTTPS**: Served via Traefik with Let's Encrypt SSL
- **Ingress Controller**: Traefik v3.0

### 🔧 Access Methods:

#### Production Access:
```
https://drive-migration.osp.com.vn
```

#### Local Access (Port Forwarding):
```bash
kubectl port-forward svc/drive-migration 3000:3000
# Access: http://localhost:3000
```

### 📁 File Structure:
```
drive-migration/
├── 01-deployment.yaml     # Deployment and Service
└── 02-ingress.yaml        # IngressRoute and SSL configuration
```

### 🛠️ Management Commands:

#### Deploy Service:
```bash
./deploy-drive-migration.sh
```

#### Remove Service:
```bash
./cleanup-drive-migration.sh
```

#### Check Status:
```bash
# Check pods
kubectl get pods -l app=drive-migration

# Check service
kubectl get svc drive-migration

# Check ingress routes
kubectl get ingressroutes drive-migration

# Check logs
kubectl logs -l app=drive-migration
```

### 🔒 Security Features:
- ✅ Non-root container execution
- ✅ Dropped capabilities
- ✅ Resource limits configured
- ✅ Health checks (liveness and readiness probes)
- ✅ HTTPS enforcement
- ✅ Automatic HTTP to HTTPS redirect

### 📊 Resource Configuration:
- **Memory**: 256Mi request, 512Mi limit
- **CPU**: 250m request, 500m limit
- **Replicas**: 1 (can be scaled)

### 🌍 DNS Configuration:
To make the service accessible via `https://drive-migration.osp.com.vn`, you need to:

1. **Get your cluster's external IP**:
   ```bash
   kubectl get svc -n traefik-system traefik
   ```

2. **Configure DNS**:
   - Create an A record pointing `drive-migration.osp.com.vn` to your cluster's external IP
   - Or add to your local `/etc/hosts` file for testing:
     ```
     127.0.0.1 drive-migration.osp.com.vn
     ```

### 🔐 SSL Certificate:
- **Provider**: Let's Encrypt
- **Auto-renewal**: Enabled
- **HTTP Challenge**: Configured via Traefik

### 🚨 Troubleshooting:

#### Check Pod Status:
```bash
kubectl get pods -l app=drive-migration
kubectl describe pod -l app=drive-migration
```

#### Check Logs:
```bash
kubectl logs -l app=drive-migration -f
```

#### Check Ingress:
```bash
kubectl get ingressroutes drive-migration -o yaml
```

#### Test Local Access:
```bash
kubectl port-forward svc/drive-migration 3000:3000
curl http://localhost:3000
```

### 🔄 Scaling:
To scale the service:
```bash
kubectl scale deployment drive-migration --replicas=3
```

### 📝 Environment Variables:
Currently configured:
- `PORT=3000`

Add more environment variables by editing `drive-migration/01-deployment.yaml`:
```yaml
env:
- name: PORT
  value: "3000"
- name: YOUR_VAR
  value: "your-value"
```

### 🎯 Next Steps:
1. **Configure DNS** to point to your cluster's external IP
2. **Monitor logs** for any application-specific issues
3. **Scale if needed** based on traffic requirements
4. **Configure monitoring** and alerts
5. **Set up backup** if the application stores data

The service is now fully deployed and ready for production use! 🎉
