apiVersion: apps/v1
kind: Deployment
metadata:
  name: drive-migration
  namespace: default
  labels:
    app: drive-migration
spec:
  replicas: 0
  selector:
    matchLabels:
      app: drive-migration
  template:
    metadata:
      labels:
        app: drive-migration
    spec:
      containers:
      - name: drive-migration
        image: vietprogrammer/drive-migration:latest
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: GOOGLE_CLIENT_ID
          value: "509840761969-nqns1no3df6s1k38rcuhrdrv3mto1ftf.apps.googleusercontent.com"
        - name: GOOGLE_CLIENT_SECRET
          value: "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"
        - name: SENTRY_DSN
          value: "https://<EMAIL>/4509624571527168"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
---
apiVersion: v1
kind: Service
metadata:
  name: drive-migration
  namespace: default
  labels:
    app: drive-migration
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    name: http
  selector:
    app: drive-migration
