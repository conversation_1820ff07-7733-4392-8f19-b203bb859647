apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: drive-migration
  namespace: default
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`drive-migration.osp.com.vn`) && !PathPrefix(`/po`)
      kind: Rule
      services:
        - name: drive-migration
          port: 3000
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: drive-migration-http
  namespace: default
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`drive-migration.osp.com.vn`) && !PathPrefix(`/po`)
      kind: Rule
      services:
        - name: drive-migration
          port: 3000
      middlewares:
        - name: https-redirect
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: https-redirect
  namespace: default
spec:
  redirectScheme:
    scheme: https
    permanent: true
