#!/bin/bash

# Cleanup script for drive-migration service

set -e

echo "🧹 Cleaning up drive-migration service..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Remove drive-migration service
echo -e "${YELLOW}🗑️ Removing drive-migration service...${NC}"
kubectl delete -f drive-migration/ --ignore-not-found=true

echo -e "${GREEN}✅ Drive-migration service cleanup completed!${NC}"
