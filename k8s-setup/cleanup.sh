#!/bin/bash

# Cleanup script for <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>

set -e

echo "🧹 Cleaning up <PERSON>raefik and Portainer..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Remove Portainer
echo -e "${YELLOW}🐳 Removing Portainer...${NC}"
kubectl delete -f portainer/ --ignore-not-found=true

# Remove Traefik
echo -e "${YELLOW}🌐 Removing Traefik...${NC}"
kubectl delete -f traefik/ --ignore-not-found=true

# Remove namespaces
echo -e "${YELLOW}📁 Removing namespaces...${NC}"
kubectl delete namespace portainer --ignore-not-found=true
kubectl delete namespace traefik-system --ignore-not-found=true

echo -e "${GREEN}✅ Cleanup completed!${NC}"
